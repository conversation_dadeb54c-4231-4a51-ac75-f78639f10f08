import React, { useState, useEffect, useContext, useRef } from "react";
import { ImBin } from "react-icons/im";
import DebugBreadcrumb from "../TestComponents/DebugBreadcrumb";
import axios from 'axios';
import { observer } from 'mobx-react';
import classNames from "classnames";
import { AppProviderStore } from "../../AppStore";
import { v4 as uuidv4 } from 'uuid';

const AudioRecorder = ({ memos }) => {
  const { AppStore } = useContext(AppProviderStore);
  const initMemos = memos ? memos : null;
  let trackMemos = AppStore.memos.audioMemos;
  const [memoRecordingToggle, setMemoRecordingToggle] = useState(false);
  const [mediaStream, setMediaStream] = useState(null);
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const [seconds, setSeconds] = useState(0);
  const [changedWhileRecording, setChangedWhileRecording] = useState(false);
  const [recordingTrackId, setRecordingTrackId] = useState(null);
  const intervalRef = useRef();

  useEffect(() => {
    if (memoRecordingToggle) {
      intervalRef.current = setInterval(() => {
        setSeconds((prev) => prev + 1);
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
      setSeconds(0);
    }
  }, [memoRecordingToggle]);

  useEffect(() => {
    // Clean up the media stream and media recorder when the component unmounts
    return () => {
      if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop());
      }
      if (mediaRecorder) {
        mediaRecorder.removeEventListener('dataavailable', handleDataAvailable);
      }
    };
  }, [mediaStream, mediaRecorder]);

  /**
   * This useEffect sorta works:
   * Memos stop recording on change track but doesnt render
   * the recording till another memo renders
   */
  // useEffect(()=>{
  //   console.log('useEffect triggered on changedTrack')
  //   if(memoRecordingToggle){
  //     setChangedWhileRecording(true)
  //     stopRecording
  //     setMemoRecordingToggle(false)
  //   }
  // },[AppStore.currentTrack])

  const startRecording = () => {
    console.log(AppStore.judge.name, 'is recording a memo')
    // Capture the current track ID when recording starts
    setRecordingTrackId(AppStore.currentTrack._id);
    AppStore.setIsRecording()
    navigator.mediaDevices.getUserMedia({
      audio: {
        autoGainControl: false,
        echoCancellation: false,
        noiseSuppression: false
      }
    })
      .then(stream => {
        const recorder = new MediaRecorder(stream);
        recorder.addEventListener('dataavailable', handleDataAvailable);
        recorder.start();
        setMediaStream(stream);
        setMediaRecorder(recorder);
        setSeconds(0); // Reset the timer when starting a new recording
        setMemoRecordingToggle(true);
      })
      .catch(error => {
        console.error('Error accessing microphone:', error);
        // Reset the recording track ID if there's an error
        setRecordingTrackId(null);
        AppStore.setIsRecording(); // Toggle back if there was an error
      });
  };

  const stopRecording = () => {
    console.log(AppStore.judge.name, 'finished recording a memo')
    AppStore.setIsRecording();
    if (mediaRecorder && mediaStream) {
      mediaRecorder.stop();
      mediaStream.getTracks().forEach(track => track.stop());
      setMediaStream(null);
      setMediaRecorder(null);
      setMemoRecordingToggle(false);
      // Note: Don't clear recordingTrackId here - we need it for saving
    }
  };


	  // Format seconds as mm:ss for a clearer timer display
	  const formatTime = (s) => {
	    const m = Math.floor(s / 60);
	    const ss = s % 60;
	    return `${String(m).padStart(2, '0')}:${String(ss).padStart(2, '0')}`;
	  };

  const handleDataAvailable = (event) => {
    console.log('data available')
    // const audioBlob = new Blob([event.data], { type: 'audio/wav' });
    // const audioURL = URL.createObjectURL(audioBlob);
    addAudioElement(event.data);
    //setURL(audioURL);
  };

  const addAudioElement = async (blob_file) => {
    console.log('Add Memo')
    console.log(blob_file)

    // Set saving state to prevent navigation
    AppStore.setIsSavingMemo(true);

    try {
      const mimeType = blob_file.type;
      console.log(mimeType);
      const match = /audio\/(.+);/.exec(mimeType);
      let fileType = 'unknown'; // Default in case extraction fails
      if (match && match.length > 1) {
        fileType = match[1];
      }


      if(fileType === 'unknown'){
        console.warn('Unknown file type for MIME type: ', mimeType)
        console.log('Attempting to format it to .wav')
        //might need to look into an actual conversion method
        let logEvent = {
          issue: "Unknown Audio Memo Recording filetype - Setting to .WAV",
          fileType: mimeType,
          judge: AppStore.judge,
          trackId: recordingTrackId || AppStore.currentTrack._id
        }
        axios.post(`/api/log`, logEvent);
        fileType = ".wav"
      }

      let memoFile = `${uuidv4()}.${fileType}`
      let year = new Date().getFullYear()
      // Use the captured track ID from when recording started, not the current track
      let trackIdToUse = recordingTrackId || AppStore.currentTrack._id;
      let fullPath = `${year}/judges/${AppStore.judge.name}/${trackIdToUse}/${memoFile}`
      let body = {
        filename: fullPath
      }
      let presignedRes = await axios.post(`/api/minioPresignedUrl`,body)
      let presignedUrl = presignedRes.data.presigned
      await axios.put(presignedUrl,blob_file);

      let memoUrl = `https://app.foundationformusiceducation.org/fme-app/${fullPath}`
      AppStore.addAudioMemo(memoUrl)

      console.log('Memo saved successfully for track:', trackIdToUse);
    } catch (error) {
      console.error('Error saving memo:', error);
    } finally {
      // Clear saving state and reset recording track ID
      AppStore.setIsSavingMemo(false);
      setRecordingTrackId(null);
    }
  };

  const handleDeleteAudioMemo = (index) => {
    AppStore.deleteAudioMemo(AppStore.memos.audioMemos[index]);
  };

  return (
    <div className="bg-white">
      <DebugBreadcrumb breadcrumbs={"/Components/WorkViewComponents/MemoRecorder"} />
      <div className="bg-header px-4 py-3">
        <h2 className="text-white font-semibold">Memos</h2>
      </div>
      <div className="p-4">
        <div id="audio_memos">
          {AppStore.memos.audioMemos && AppStore.memos.audioMemos.length > 0 ? (
            <div className="bg-[#d7d7d7] p-4 mb-4 gap-y-3 flex flex-col">
              {AppStore.memos.audioMemos.map((memo, index) => {
                let memoid = `memo_${index}`;
                return (
                  <div
                    key={index}
                    className={classNames(
                      "px-4 py-3 rounded-md bg-white flex justify-between items-center gap-4"
                    )}
                  >
                    <div className="flex gap-4 items-center">
                      <label key={memoid} className="text-grayText font-semibold">
                        Memo {index + 1}
                      </label>
                      <audio id={memoid} src={memo.content} controls />
                    </div>
                    {String(memo.judge) === String(AppStore.judge?._id) && (
                      <button onClick={() => handleDeleteAudioMemo(index)}>
                        <ImBin color="tomato" size={18} />
                      </button>
                    )}
                  </div>
                );
              })}
            </div>
          ) : (
            <p className="text-iconGreen font-semibold px-4 py-1 w-fit mb-4 rounded-3xl border border-1 border-iconGreen">
              No memos recorded
            </p>
          )}
        </div>
        <div className="flex items-center gap-6 p-4 bg-[#F9FAFB] rounded-md border">
          <style>{`
            @keyframes pulse {
              0% { box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.6);}
              70% { box-shadow: 0 0 0 24px rgba(220, 38, 38, 0);}
              100% { box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);}
            }
            @keyframes blink { 0%, 100% { opacity: 1; } 50% { opacity: 0.4; } }
          `}</style>

          <button
            onClick={memoRecordingToggle ? stopRecording : startRecording}
            aria-pressed={memoRecordingToggle}
            aria-label={memoRecordingToggle ? 'Stop recording memo' : 'Start recording memo'}
            className="focus:outline-none"
            style={{
              width: '96px',
              height: '96px',
              borderRadius: '9999px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              border: '6px solid #ffffff',
              boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
              backgroundColor: memoRecordingToggle ? '#DC2626' : '#E5E7EB',
              color: memoRecordingToggle ? '#ffffff' : '#374151',
              animation: memoRecordingToggle ? 'pulse 1.5s infinite' : 'none',
              cursor: 'pointer'
            }}
          >
            <i
              className={memoRecordingToggle ? 'fas fa-stop' : 'fas fa-microphone'}
              style={{ fontSize: '36px', lineHeight: 1 }}
            />
          </button>

          <div className="flex flex-col gap-1">
            <div className="flex items-center gap-3">
              <h3 className="text-iconGreen font-semibold">
                {memoRecordingToggle ? 'Recording memo' : 'Record memo'}
              </h3>
              {memoRecordingToggle && (
                <div className="flex items-center gap-2">
                  <span
                    aria-hidden
                    style={{
                      width: '10px',
                      height: '10px',
                      backgroundColor: '#DC2626',
                      borderRadius: '9999px',
                      animation: 'blink 1s infinite'
                    }}
                  />
                  <span className="font-mono text-sm" style={{ color: '#DC2626' }}>
                    {formatTime(seconds)}
                  </span>
                </div>
              )}
            </div>
            <p className="text-sm text-gray-500">
              {memoRecordingToggle
                ? 'Click the red button to stop and save your memo.'
                : 'Click the button to start recording a memo.'}
            </p>
            {AppStore.isSavingMemo && (
              <div className="text-orange-600 font-semibold mt-2">Saving memo...</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default observer(AudioRecorder);
